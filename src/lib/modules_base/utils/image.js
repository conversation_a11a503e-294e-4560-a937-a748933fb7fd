import Taro from '@tarojs/taro';

// 选择图片
async function chooseImage(opts) {
  let res = {
    tempFiles: [],
  };
  try {
    if (Taro.chooseMedia) {
      res = await Taro.chooseMedia({
        mediaType: ['image'],
        ...opts,
      });
    } else {
      res = await Taro.chooseImage(opts).then(
        ({
          apFilePaths,
          tempFilePaths = apFilePaths,
          tempFiles = tempFilePaths.map((item) => ({ path: item, size: 0 })),
        }) => ({
          tempFiles: tempFiles.map(({ path, ...restItem }) => ({
            ...restItem,
            tempFilePath: path,
          })),
        }),
      );
    }
  } catch (error) {
    throw error;
  }
  return res;
}

// 计算压缩质量
function countQuality({ size, maxSize }) {
  if (!size || size === 0) return 100; // 防止除以0
  const ratio = maxSize / size;
  const quality = Math.min(Math.max(Math.round(ratio * 100), 1), 100);
  return quality;
}

// 压缩图片
async function compressImage(src, opts) {
  const { maxSize, compressedWidth, compressedHeight } = opts;
  try {
    if (maxSize || compressedWidth || compressedHeight) {
      const compressOpts = {
        compressedWidth,
        compressedHeight,
        src,
        quality: 100,
      };
      if (maxSize) {
        const { size } = await Taro.getFileInfo({ filePath: src });
        // 质量压缩
        if (size > maxSize) {
          const quality = countQuality({ maxSize, size });
          if (quality) {
            compressOpts.quality = quality;
          }
        }
      }

      const res = await Taro.compressImage(compressOpts);

      return res;
    }
  } catch (error) {}

  return {
    tempFilePath: src,
  };
}

// 剪裁图片
async function cropImage(src, cropScale) {
  try {
    if (cropScale) {
      const res = await Taro.cropImage({ src, cropScale });
      return res;
    }
  } catch (error) {}

  return {
    tempFilePath: src,
  };
}

// 图片选择
export function chooseImageByActionSheet(opts, page) {
  return new Promise((resolve, reject) => {
    const sourceTypes = ['camera', 'album'];
    const {
      cropScale,
      count = 1,
      maxSize, // 最大尺寸，判断是否需要压缩
      compressedWidth,
      compressedHeight,
    } = opts || {};
    Taro.kbActionSheet(
      {
        items: ['拍照', '从相册选取'],
        onClick: async (index) => {
          try {
            const { tempFiles } = await chooseImage({
              count,
              sourceType: [sourceTypes[index]],
            });
            const tempFilePaths = tempFiles.map((item) => item.tempFilePath);

            // 支持多张图片的压缩与剪裁
            const processedResults = await Promise.allSettled(
              tempFilePaths.map(async (filePath, index) => {
                try {
                  let newFilePath = filePath;

                  // 先按照 compressedWidth或compressedHeight压缩一次
                  const compress1Res = await compressImage(newFilePath, {
                    compressedWidth,
                    compressedHeight,
                  });
                  newFilePath = compress1Res.tempFilePath;

                  // 剪裁（注意：多张图片时，剪裁可能不适用，但保持兼容性）
                  if (count === 1 && cropScale) {
                    const cropRes = await cropImage(newFilePath, cropScale);
                    newFilePath = cropRes.tempFilePath;
                  }

                  // 后压缩
                  const compress2Res = await compressImage(newFilePath, {
                    maxSize,
                  });
                  newFilePath = compress2Res.tempFilePath;

                  return {
                    success: true,
                    filePath: newFilePath,
                    index,
                  };
                } catch (error) {
                  // 压缩失败时返回原文件
                  console.warn(`图片压缩失败，使用原文件: ${filePath}`, error);
                  return {
                    success: false,
                    filePath: filePath,
                    index,
                    error,
                  };
                }
              }),
            );

            // 更新处理后的文件路径
            processedResults.forEach((result) => {
              if (result.status === 'fulfilled') {
                const { filePath, index } = result.value;
                tempFilePaths[index] = filePath;
                tempFiles[index].tempFilePath = filePath;
              }
            });

            resolve({
              tempFilePaths,
              tempFiles,
            });
          } catch (error) {
            reject(error);
          }
        },
      },
      page,
    );
  });
}
