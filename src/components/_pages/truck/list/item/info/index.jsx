import { View } from '@tarojs/components';
import Taro from '@tarojs/taro';
import classNames from 'classnames';
import BatteryIcon from '../../../battery';
import TruckStatus from '../status';
import TruckServerStatus from '../status/server';
import './index.scss';

const TruckInfo = (props) => {
  const { data, border = true, className, isDetail, isRenewal } = props;

  const rootCls = classNames('truck-info kb-navigator kb-navigator-ghost', className, {
    'kb-navigator-noborder': !border,
    'kb-navigator-noarrow': isDetail || isRenewal,
  });

  // 跳转详情
  const handleClick = (e) => {
    e.stopPropagation();
    if (isDetail || isRenewal) return;
    Taro.navigator({
      url: 'truck/detail',
      options: {
        vehicle_no: data?.vehicle_no,
      },
    });
  };

  return (
    <View
      className={rootCls}
      hoverClass={isDetail || isRenewal ? 'none' : 'kb-hover'}
      hoverStopPropagation
      onClick={handleClick}
    >
      <View className='kb-navigator__content at-row at-row__align--center'>
        <View>{data?.vehicle_no}</View>
        <TruckStatus data={data} />
        <BatteryIcon quantity={data?.battery_power} />
      </View>
      {!isDetail && (
        <View className='kb-navigator__extra'>
          <TruckServerStatus data={data} strong />
        </View>
      )}
    </View>
  );
};

export default TruckInfo;
