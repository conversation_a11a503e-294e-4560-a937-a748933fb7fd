/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { Text, View } from '@tarojs/components';
import { useMemo, useRef, useState } from 'react';
import { AtIcon } from 'taro-ui';
import RentalTimePicker from '~/components/_pages/index/client/rental/timeRange/picker';
import { dateFormat, timeInterval } from '~/components/_pages/index/client/rental/timeRange/utils';
import './index.scss';

const RentalOrderDetailDate = ({ data, onChange }) => {
  const isEdit = useMemo(() => !!onChange, [onChange]);
  const [renewalDate, setRenewalDate] = useState(null);
  const pickerRef = useRef(null);

  const date = useMemo(() => {
    const start = dateFormat(data?.lease_start_day);
    const end = dateFormat(data?.lease_end_day);
    const interval = timeInterval([data?.lease_start_day, data?.lease_end_day]);
    return { start, end, interval };
  }, [data]);

  const pickerValue = useMemo(() => {
    return [data?.lease_start_day, data?.lease_end_day, renewalDate];
  }, [renewalDate, data]);

  const { start, end, interval } = date;

  const handlePickerChange = (v) => {
    setRenewalDate(v);
    onChange(v);
  };

  return (
    <View className='kb-box kb-spacing-md-lr kb-rental-order-date'>
      <View className='kb-size__lg kb-border-b kb-spacing-md-tb'>租赁日期</View>
      <View className='kb-line'>
        <View className='kb-dot' />
        <View className='kb-dot kb-dot__end' />
      </View>
      <View className='at-row at-row__align--end at-row__justify--between'>
        <View>
          <View className='kb-size__xl kb-color__black kb-spacing-sm-b'>{start?.date}</View>
          <View className='kb-size__base kb-color__grey'>{start?.time}</View>
          <View className='kb-spacing-md-tb kb-size__base2'>起租时间</View>
        </View>
        <View className='time_interval'>
          共{interval.day ? `${interval.day}天` : ''}
          {interval.hour ? `${interval.hour}小时` : ''}
        </View>
        <View>
          <View className='kb-size__xl kb-color__black kb-spacing-sm-b'>{end?.date}</View>
          <View className='kb-size__base kb-color__grey'>{end?.time}</View>
          <View className='kb-spacing-md-tb kb-size__base2'>到期时间</View>
        </View>
        {isEdit ? (
          <>
            {!renewalDate ? (
              <View className='kb-rental-date-picker' hoverClass='kb-hover-opacity'>
                选择续租时间
                <AtIcon
                  prefixClass='kb-icon'
                  value='arrow'
                  className='kb-size__xs kb-color__brand'
                />
              </View>
            ) : (
              <>
                <View className='time_interval'>
                  共{interval.day ? `${interval.day}天` : ''}
                  {interval.hour ? `${interval.hour}小时` : ''}
                </View>
                <View>
                  <View className='kb-size__xl kb-color__black kb-spacing-sm-b'>{end?.date}</View>
                  <View className='kb-size__base kb-color__grey'>{end?.time}</View>
                  <View className='kb-spacing-md-tb kb-size__base2'>续租时间</View>
                </View>
              </>
            )}
          </>
        ) : null}
      </View>
      {!isEdit && (
        <View className='kb-size__base2'>
          收车地址 <Text className='kb-spacing-md-l'>{data?.stop_name}</Text>
        </View>
      )}
      {isEdit && (
        <RentalTimePicker ref={pickerRef} value={pickerValue} onChange={handlePickerChange} />
      )}
    </View>
  );
};

RentalOrderDetailDate.options = {
  addGlobalClass: true,
};

export default RentalOrderDetailDate;
