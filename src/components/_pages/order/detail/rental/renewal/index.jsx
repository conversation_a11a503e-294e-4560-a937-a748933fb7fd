/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { View } from '@tarojs/components';
import classNames from 'classnames';
import { useMemo } from 'react';

const RentalOrderRenewal = ({ data }) => {
  const list = [
    {
      label: '续租',
      key: 'day',
    },
    {
      label: '日租金',
      key: 'day_rent',
    },
  ];
  const _data = useMemo(() => {
    const {
      date: { day, hour },
      day_rent,
    } = data;
    const total = day * day_rent + (hour * day_rent) / 24;
    return {
      ...data,
      total,
      date: `${day ? `${day}天` : ''}${hour ? `${hour}小时` : ''}`,
    };
  }, [data]);
  return (
    <View className='kb-box kb-spacing-md-lr'>
      <View className='kb-size__lg kb-border-b kb-spacing-md-tb'>续租明细</View>
      {list.map((item, index) => (
        <View
          key={item.key}
          className={classNames(
            'at-row at-row__align--center at-row__justify--between kb-spacing-md-b',
            {
              'kb-spacing-md-t': index === 0,
            },
          )}
        >
          <View className='kb-size__base kb-color__greyer'>{item.label}</View>
          <View className='kb-size__base2'>{_data?.[item.key] || ''}</View>
        </View>
      ))}
      <View className={classNames('at-row at-row__align--center kb-spacing-md-tb kb-border-t')}>
        <View>合计</View>
        <View>￥{_data?.total || ''}</View>
      </View>
    </View>
  );
};

RentalOrderRenewal.options = {
  addGlobalClass: true,
};

export default RentalOrderRenewal;
