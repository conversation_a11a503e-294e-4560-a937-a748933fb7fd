/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import KbListBox from '@/components/_pages/order/listbox';
import { View } from '@tarojs/components';
import { useMemo, useState } from 'react';
import { AtTextarea } from 'taro-ui';
import { useUserType } from '~/components/_pages/userTypeLayout/_utils';
import { cancelRentOrder } from '~/services/rental';
import FloatLayoutExtend from '../float-layout';
import './index.scss';

const CancelOperate = (props) => {
  const { trigger, data, onRefresh } = props;
  const [value, setValue] = useState('');
  const [selected, setSelected] = useState('');
  const [focus, setFocus] = useState(false);
  const { isServer } = useUserType();

  const list = useMemo(() => {
    return isServer
      ? [
          {
            label: '协商取消',
            value: '1',
          },
          {
            label: '车辆出现故障',
            value: '2',
          },
          {
            label: '车辆发生交通事故',
            value: '3',
          },
          {
            label: '其他原因',
            value: '4',
          },
        ]
      : [
          {
            label: '和车主协商取消',
            value: '1',
          },
          {
            label: '计划有变',
            value: '2',
          },
          {
            label: '车子超时未到达',
            value: '3',
          },
          {
            label: '其他原因',
            value: '4',
          },
        ];
  }, [isServer]);

  const onSelectChange = (v) => {
    setSelected(v.label);
    setValue(v.label);
  };

  const onConfirm = async () => {
    const isSuccess = await cancelRentOrder({ order_id: data.order_id, reason: value });
    if (isSuccess) {
      onRefresh();
    }
    return isSuccess;
  };

  const onVisibleChange = (isOpened) => {
    setFocus(isOpened);
  };

  return (
    <FloatLayoutExtend
      trigger={trigger}
      title='取消订单原因'
      onConfirm={onConfirm}
      onVisibleChange={onVisibleChange}
    >
      <View className='kb-spacing-md kb-cancel'>
        <KbListBox
          className='kb-package__base'
          selectedActive='ghost'
          list={list}
          onChange={onSelectChange}
          selectted={selected}
        />
        <View className='kb-spacing-md-tb'>
          <AtTextarea
            className='kb-cancel-textarea'
            placeholder='请输入取消订单原因，最多100字'
            maxLength={100}
            count
            focus={focus}
            value={value}
            onChange={(v) => setValue(v)}
          />
        </View>
      </View>
    </FloatLayoutExtend>
  );
};

CancelOperate.options = {
  addGlobalClass: true,
};

export default CancelOperate;
