/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { View } from '@tarojs/components';
import { useMemo, useState } from 'react';
import { AtButton } from 'taro-ui';
import { rentOrderDetailApi } from '~/services/rental';
import LongList from '~base/components/long-list';
import { useLongList } from '~base/components/long-list/hooks';
import PageLayout from '~base/components/page/layout';
import { timeInterval } from '../../index/client/rental/timeRange/utils';
import RentalTruckCard from '../../truck/choose/rental/list/card';
import RentalOrderDetailDate from '../detail/rental/date';
import RentalOrderRenewal from '../detail/rental/renewal';
import './index.scss';

const OrderRenewal = ({ order_id }) => {
  const [value, setValue] = useState();
  const { config, data } = useLongList(rentOrderDetailApi, {
    api: {
      data: {
        order_id,
      },
    },
    isNonList: true,
  });

  const handleSubmit = async () => {
    console.log(value, 'handleSubmit');
  };

  const renewalData = useMemo(() => {
    if (!value || !data) return null;
    const date = timeInterval(data.lease_end_day, value);
    return {
      day_rent: data?.vehicle_info?.lease_config?.rent,
      date,
    };
  }, [value, data]);

  return (
    <PageLayout
      renderFooter={
        <View className='kb-background__white kb-spacing-md'>
          <AtButton type='primary' circle onClick={handleSubmit}>
            续租
          </AtButton>
        </View>
      }
    >
      <View className='order-detail-settle'>
        <LongList data={config}>
          {data && (
            <View className='kb-spacing-md'>
              <RentalTruckCard data={data} source='renewal' />
              <RentalOrderDetailDate data={data} onChange={setValue} />
              {renewalData && <RentalOrderRenewal data={renewalData} />}
            </View>
          )}
        </LongList>
      </View>
    </PageLayout>
  );
};

OrderRenewal.options = {
  addGlobalClass: true,
};

export default OrderRenewal;
